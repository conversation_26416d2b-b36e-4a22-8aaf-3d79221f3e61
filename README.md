# 🤖 DaBot Agent - LangGraph Edition

A simplified conversational AI agent built with LangGraph, designed to work with local LLaMA.cpp models for CPU-only environments.

## 🎯 Key Features

- **🏗️ Simplified LangGraph Flow**: Clean START → LLM → END architecture
- **🚀 Local LLM Support**: Optimized for LLaMA.cpp server integration
- **📝 Proper Message Patterns**: Follows LangGraph best practices with System/Human/AI messages
- **⚙️ Centralized Configuration**: All constants managed in single config.py module
- **🖥️ Interactive CLI**: Multiple interaction modes (interactive, demo, test)
- **🚀 CPU Optimized**: Designed for CPU-only inference with efficient prompting
- **📊 Comprehensive Logging**: Function-level logging with @log decorator

## 🏗️ Architecture

The agent uses a simplified flow implemented through LangGraph:

```
START → llm_node → END
```

### Current Flow Diagram

```mermaid
graph TD
    A[User Input] --> B[main.py]
    B --> C[AgentOrchestrator.run_conversation]
    C --> D[create_initial_state]
    D --> E[AgentState]
    E --> F[LangGraph.stream]
    F --> G[llm_node]

    G --> H[Extract user_input from state]
    H --> I[Create LlamaCppProvider]
    I --> J[Get LLM instance]
    J --> K[Create Messages Array]
    K --> L[SystemMessage: 'You are a helpful AI assistant...']
    L --> M[HumanMessage: user_input]
    M --> N[LLM.invoke with messages]
    N --> O[LLM Response]
    O --> P[Update state with final_answer]
    P --> Q[Add AIMessage to messages]
    Q --> R[Return updated state]

    R --> S[Extract conversation result]
    S --> T[Return to user]
```

### Message Flow

The agent follows LangGraph best practices with proper message roles:

1. **SystemMessage**: Sets the AI assistant context and behavior
2. **HumanMessage**: Contains the user's input/question
3. **AIMessage**: Contains the LLM's response

## 🚀 Quick Start

### Prerequisites

- Python 3.8+
- LLaMA.cpp server running (default: `http://************:11111/completion`)
- Virtual environment recommended

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd dabot-agent
```

2. Create and activate virtual environment:
```bash
python -m venv .venv-dabot-agent
source .venv-dabot-agent/bin/activate  # Linux/Mac
# or
.venv-dabot-agent\Scripts\activate  # Windows
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Set up environment variables (optional):
```bash
export LLAMACPP_API_URL="http://your-server:port/completion"
export LOG_LEVEL="DEBUG"
```

### Running the Agent

```bash
python main.py
```

Choose from the available modes:
1. **Interactive Mode**: Chat with the agent
2. **Demo Mode**: See predefined scenarios
3. **Test Connection**: Verify LLM server connectivity

## ⚙️ Configuration

All configuration is centralized in `config.py`. Key constants include:

### LLM Configuration
- `LLAMACPP_API_URL`: LLaMA.cpp server endpoint
- `LLAMACPP_MAX_TOKENS`: Maximum tokens for responses (200)
- `MODEL_PROVIDER`: Model provider type ("llamacpp")

### Agent Configuration
- `MAX_ITERATIONS`: Maximum reasoning steps (5)
- `MAX_MEMORY_TOKENS`: Memory token limit (500)
- `VERBOSE`: Enable verbose output

### Logging Configuration
- `LOG_LEVEL`: Logging level ("DEBUG")
- `LOG_FILE`: Log file path ("dabot-agent.log")

### Prompt Manager Configuration
- `MAX_CONTEXT_TOKENS`: Maximum context window (2048)
- `RESERVED_TOKENS`: Reserved tokens for response (50)

## 📁 Project Structure

```
dabot-agent/
├── config.py              # Centralized configuration constants
├── main.py                # CLI entry point
├── core/                  # Core LangGraph components
│   ├── graphs/           # Graph definitions and orchestration
│   ├── nodes/            # LLM reasoning node
│   └── state.py          # Simplified state management
├── models/               # LLM provider implementations
├── tools/                # Tool registry (preserved for future use)
├── utils/                # Utility functions
└── logger/               # Logging utilities with @log decorator
```

## 📊 State Management

The simplified `AgentState` contains:

- `messages`: List of conversation messages (System/Human/AI)
- `user_input`: Current user input string
- `final_answer`: LLM response
- `error`: Optional error information

## 🤖 LLM Integration

The system uses proper LangGraph message patterns:

```python
messages = [
    SystemMessage(content="You are a helpful AI assistant..."),
    HumanMessage(content=user_input)
]
response = llm.invoke(messages)
```

## 🔧 Tools (Preserved for Future Use)

The tool system is preserved but not used in the current simple flow:

- **get_current_date**: Get today's date
- **get_current_time**: Get current time
- **get_current_datetime**: Get both date and time
- **math_calculator**: Perform mathematical calculations

## 🛠️ Development

### Adding New Functionality

1. **Extend the Graph**: Modify `core/graphs/agent_graph.py`
2. **Add New Nodes**: Create nodes in `core/nodes/`
3. **Update State**: Modify `core/state.py` as needed
4. **Configuration**: Add constants to `config.py`

### Logging

All functions use the `@log` decorator for comprehensive logging:

```python
from logger.get_logger import log

@log
def my_function():
    # Function implementation
    pass
```

## 🔧 Troubleshooting

### Common Issues

1. **Connection Error**: Ensure LLaMA.cpp server is running and accessible
2. **Import Errors**: Verify all dependencies are installed in virtual environment
3. **Configuration Issues**: Check `config.py` constants and environment variables

### Debugging

The system provides comprehensive logging at DEBUG level:

```bash
export LOG_LEVEL="DEBUG"
python main.py
```

## 📄 License

[Add your license information here]

## 🤝 Contributing

[Add contribution guidelines here]

| Variable | Default | Description |
|----------|---------|-------------|
| `LLAMACPP_API_URL` | `http://************:11111/completion` | LLaMA.cpp server URL |
| `MAX_ITERATIONS` | `5` | Maximum reasoning steps |
| `LLAMACPP_TEMPERATURE` | `0.1` | LLM temperature |
| `LLAMACPP_MAX_TOKENS` | `2048` | Max tokens per response |
| `VERBOSE` | `false` | Show detailed reasoning |
| `LOG_LEVEL` | `INFO` | Logging level |

### LLaMA.cpp Server Setup

Your server should be running with these recommended parameters:
```bash
# Example server command
./server -m your-model.gguf -c 4096 -t 8 --host 0.0.0.0 --port 11111
```

## 🧪 Testing

Test the setup without LLM server:
```bash
python test_langgraph_setup.py      # Basic setup test
python test_agent_functionality.py  # Core functionality
python test_agent_with_mock.py      # Mock LLM test
```

## 💡 Example Usage

### Programmatic Usage
```python
from core.graphs.orchestrator import AgentOrchestrator

# Initialize agent
orchestrator = AgentOrchestrator(max_steps=5)

# Run conversation
result = orchestrator.run_conversation("What's 2 + 2?")
print(result["final_answer"])  # "2 + 2 equals 4"

# Streaming conversation
for step in orchestrator.stream_conversation("Calculate sqrt(16)"):
    print(f"Step: {step['step_type']}")
    if step.get('reasoning'):
        print(f"Thinking: {step['reasoning'][-1]}")
```

## 🔄 Decision Flow

```mermaid
graph TD
    A[User Input] --> B[LLM Reasoning]
    B --> C{Tool Needed?}
    C -->|Yes| D[Select Tool]
    C -->|No| E[Direct Answer]
    D --> F[Execute Tool]
    F --> G[Process Result]
    G --> H[Final Answer]
    E --> H
    H --> I[Return to User]
```

## 🚨 Troubleshooting

### Common Issues

1. **Import Errors**: Make sure you're in the project directory and using the correct Python environment
2. **Connection Failed**: Verify your LLaMA.cpp server is running and accessible
3. **Tool Errors**: Check that all dependencies are installed correctly

### Debug Mode
```bash
export LOG_LEVEL="DEBUG"
export VERBOSE="true"
python main.py
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🎉 Ready to Use!

Your agent is now ready for production use with intelligent tool decision-making capabilities. Start your LLaMA.cpp server and run `python main.py` to begin!
