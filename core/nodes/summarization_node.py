from langchain_core.messages import SystemMessage, HumanMessage

from core.state import Agent<PERSON><PERSON>
from logger.get_logger import log, get_logger
from .llm_node import get_cached_llm


@log
def summarization_node(state: AgentState) -> AgentState:
    """
    Summarizes conversation dynamically if messages exceed window_size,
    updates summary, and prunes old messages.
    Uses actual LLM call to produce concise summary and caches the LLM instance.

    Args:
        state: Current agent state

    Returns:
        Updated state with summary and pruned messages
    """
    # Get logger for detailed internal logging
    logger = get_logger()

    message_count = len(state.get("messages", []))
    window_size = state.get("window_size", 5)

    logger.debug(f"summarization_node - Message count: {message_count}, Window size: {window_size}")

    if message_count > window_size:
        logger.debug(
            f"summarization_node - Summarization triggered: {message_count} messages exceed window size {window_size}")

        # Get cached LLM instance
        llm = get_cached_llm(state)

        # Prepare conversation text for summarization
        messages_text = "\n".join([
                f"{msg.type}: {msg.content}"
                for msg in state.get("messages", [])
                if hasattr(msg, "content") and hasattr(msg, "type")
        ])

        # Calculate token count for conversation text
        conversation_tokens = llm.prompt_manager.count_tokens(messages_text)
        logger.debug(
                f"summarization_node - Prepared {conversation_tokens} tokens of conversation text for summarization")

        prompt = (
                "You are a helpful AI assistant. Summarize the following conversation concisely, "
                "highlighting key facts and important information the assistant should remember:\n\n"
                f"{messages_text}\n\n"
                "Provide the summary as a brief text in a way, that the conversation can continue from this point. "
        )

        summarization_msgs = [
                SystemMessage(content = "You are a conversation summarizer."),
                HumanMessage(content = prompt)
        ]

        # Calculate token counts for summarization
        system_tokens = llm.prompt_manager.count_tokens(summarization_msgs[0].content)
        prompt_tokens = llm.prompt_manager.count_tokens(summarization_msgs[1].content)
        total_summarization_tokens = system_tokens + prompt_tokens

        logger.debug(
            f"summarization_node - Summarization prompt tokens: system={system_tokens}, prompt={prompt_tokens}, total={total_summarization_tokens}")
        logger.debug(f"summarization_node - Sending summarization request to LLM: {summarization_msgs}")

        response = llm.invoke(summarization_msgs)
        new_summary = response.content.strip()

        # Calculate summary tokens
        summary_tokens = llm.prompt_manager.count_tokens(new_summary) if new_summary else 0
        logger.debug(f"summarization_node - LLM-generated summary ({summary_tokens} tokens): {new_summary}")

        state["summary"] = new_summary
        state["messages"] = state["messages"][-window_size:]
        logger.debug(f"summarization_node - Pruned messages to last {window_size} entries")
    else:
        logger.debug(f"summarization_node - Summarization not triggered: {message_count} messages below threshold")

    logger.debug(f"summarization_node - Updated state after summarization: {state}")
    return state
