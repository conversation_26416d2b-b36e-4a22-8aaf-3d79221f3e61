"""
LLM reasoning node for the LangGraph agent.

This node handles simple LLM interaction following LangGraph best practices.
"""

from typing import Dict, Any

from langchain_core.messages import AIMessage, HumanMessage, SystemMessage

from config import get_model_config, LLAMACPP_API_URL
from logger.get_logger import log, get_logger
from models.llamacpp_provider import LlamaCppProvider
from ..state import AgentState

# Module-level cache for LLM instance to avoid serialization issues
_cached_llm = None


def get_cached_llm(state: dict = None):
    """
    Returns a cached LLM instance stored at module level.
    Initializes it if missing. Uses module-level caching to avoid
    serialization issues with LangGraph state.

    Args:
        state: Agent state dictionary (unused, kept for compatibility)

    Returns:
        Cached LLM instance
    """
    global _cached_llm
    if _cached_llm is None:
        provider = LlamaCppProvider(config = get_model_config(), api_url = LLAMACPP_API_URL)
        _cached_llm = provider.get_llm()
    return _cached_llm


@log
def llm_node(state: AgentState) -> Dict[str, Any]:
    """
    LLM reasoning node following LangGraph best practices with proper message roles.
    Uses cached LLM instance and includes conversation history and summary.

    Args:
        state: Current agent state

    Returns:
        Updated state with LLM response and final_answer
    """
    # Get logger for detailed internal logging
    logger = get_logger()

    # Get user input from the latest human message or initial state
    user_input = state.get("user_input", "")
    if not user_input and state.get("messages"):
        # Extract from the most recent human message
        for msg in reversed(state["messages"]):
            if hasattr(msg, 'type') and msg.type == 'human':
                user_input = msg.content
                break

    logger.debug(f"llm_node - Processing user input: '{user_input}'")
    logger.debug(f"llm_node - Total messages in history: {len(state.get('messages', []))}")
    logger.debug(f"llm_node - Full state: {state}")

    # Get cached LLM instance for performance
    llm = get_cached_llm(state)

    # Include conversation summary in system prompt if available
    summary = state.get("summary", "")
    system_content = "You are a helpful AI assistant. Provide clear, accurate, and helpful responses to user questions."
    if summary:
        system_content += f"\n\nConversation summary:\n{summary}"
        logger.debug(f"llm_node - Using conversation summary: {summary}")

    # Use the FULL conversation history, not just the current input
    messages = [SystemMessage(content = system_content)]
    logger.debug(f"llm_node - messages: {messages}")

    # Add all conversation messages to maintain context
    conversation_messages = state.get("messages", [])
    if conversation_messages:
        messages.extend(conversation_messages)
        logger.debug(
                f"llm_node - Sending {len(messages)} messages to LLM (including system + {len(conversation_messages)} conversation messages)")
    else:
        # Fallback to just current input if no history
        messages.append(HumanMessage(content = user_input))
        logger.debug(f"llm_node - No conversation history, using current input only")

    # Calculate and log token counts for each message
    total_prompt_tokens = 0
    for i, msg in enumerate(messages):
        msg_type = type(msg).__name__
        content_preview = msg.content
        msg_tokens = llm.prompt_manager.count_tokens(msg.content)
        total_prompt_tokens += msg_tokens
        logger.debug(f"llm_node - Message {i}: {msg_type} ({msg_tokens} tokens): {content_preview}")

    logger.debug(f"llm_node - Total prompt tokens: {total_prompt_tokens}")

    # Check if we're approaching context limits
    max_context = llm.prompt_manager.max_context_tokens
    reserved = llm.prompt_manager.reserved_tokens
    available_tokens = max_context - reserved

    if total_prompt_tokens > available_tokens * 0.8:  # 80% threshold
        logger.warning(
                f"llm_node - WARNING: Approaching context limit! Using {total_prompt_tokens}/{available_tokens} tokens (80% threshold)")

    # Call LLM with full conversation context
    logger.debug(
            f"llm_node - Invoking LLM with {len(messages)} messages, {total_prompt_tokens}/{available_tokens} tokens")
    response = llm.invoke(messages)

    # Calculate response tokens
    response_tokens = llm.prompt_manager.count_tokens(response.content) if response.content else 0
    logger.debug(f"llm_node - LLM response received ({response_tokens} tokens): {response.content}")

    # Check for empty response and provide fallback
    if not response.content or response.content.strip() == "":
        logger.warning(
                f"llm_node - WARNING: LLM returned empty response! Prompt tokens: {total_prompt_tokens}, Response tokens: {response_tokens}")
        fallback_response = "I apologize, but I'm having trouble generating a response right now. Could you please rephrase your question?"
        logger.debug(f"llm_node - Using fallback response")
        final_response = fallback_response
    else:
        final_response = response.content

    # Update state with LLM response
    updated_state = {
            **state,
            "final_answer": final_response
    }

    # Add LLM response to messages following LangGraph patterns
    if "messages" not in updated_state:
        updated_state["messages"] = []
    updated_state["messages"].append(AIMessage(content = final_response))

    return updated_state
