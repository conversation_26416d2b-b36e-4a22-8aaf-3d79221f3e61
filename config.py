"""
Configuration constants for the DaBot Agent.

This module contains all configuration constants and settings for the application.
"""

import os
from typing import Dict, Any

APP_NAME = "DaBot Agent"
LLAMACPP_API_URL = "http://172.16.0.111:11111/completion"
LLAMACPP_MAX_TOKENS = "250"
MODEL_PROVIDER = "llamacpp"
MODEL_NAME = "llamacpp"
MAX_ITERATIONS = "1"
MAX_MEMORY_TOKENS = "500"
VERBOSE = "true"
LOG_LEVEL = "DEBUG"
LOG_FILE = "dabot-agent.log"
MAX_CONTEXT_TOKENS = 2048
RESERVED_TOKENS = 50
APP_VERSION = "1.0.0"
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY")


# =============================================================================
# Helper Functions
# =============================================================================
def get_model_config() -> Dict[str, Any]:
    """Get model configuration dictionary."""
    return {
            "provider"  : MODEL_PROVIDER,
            "name"      : MODEL_NAME,
            "max_tokens": LLAMACPP_MAX_TOKENS,
            "api_url"   : LLAMACPP_API_URL
    }


def get_agent_config() -> Dict[str, Any]:
    """Get agent configuration dictionary."""
    return {
            "max_iterations"   : MAX_ITERATIONS,
            "max_memory_tokens": MAX_MEMORY_TOKENS,
            "verbose"          : VERBOSE
    }


def get_logging_config() -> Dict[str, Any]:
    """Get logging configuration dictionary."""
    return {
            "level": LOG_LEVEL,
            "file" : LOG_FILE
    }
