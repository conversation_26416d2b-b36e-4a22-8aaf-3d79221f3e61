#!/usr/bin/env python3
"""
Basic setup test for DaBot Agent LangGraph Edition.

This test verifies that all core components can be imported and initialized
without errors, ensuring the basic setup is correct.
"""

import sys
import os
import unittest
from unittest.mock import patch, MagicMock

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class TestLangGraphSetup(unittest.TestCase):
    """Test basic LangGraph setup and imports."""

    def test_config_import(self):
        """Test that config module can be imported."""
        try:
            import config
            self.assertTrue(hasattr(config, 'LLAMACPP_API_URL'))
            self.assertTrue(hasattr(config, 'LLAMACPP_MAX_TOKENS'))
            self.assertTrue(hasattr(config, 'MODEL_PROVIDER'))
            print("✅ Config module imported successfully")
        except ImportError as e:
            self.fail(f"Failed to import config: {e}")

    def test_core_imports(self):
        """Test that core modules can be imported."""
        try:
            from core.state import AgentState
            from core.graphs.agent_graph import create_agent_graph
            from core.graphs.orchestrator import AgentOrchestrator
            from core.nodes.llm_node import llm_node
            from core.nodes.summarization_node import summarization_node
            print("✅ Core modules imported successfully")
        except ImportError as e:
            self.fail(f"Failed to import core modules: {e}")

    def test_models_import(self):
        """Test that model modules can be imported."""
        try:
            from models.llamacpp_provider import LlamaCppChatModel
            from models.prompt_manager import PromptManager
            print("✅ Model modules imported successfully")
        except ImportError as e:
            self.fail(f"Failed to import model modules: {e}")

    def test_logger_import(self):
        """Test that logger module can be imported."""
        try:
            from logger.get_logger import get_logger, log
            print("✅ Logger module imported successfully")
        except ImportError as e:
            self.fail(f"Failed to import logger: {e}")

    def test_tools_import(self):
        """Test that tools modules can be imported."""
        try:
            from core.tools.base import ToolRegistry, LangGraphTool
            from core.tools.local.datetime_tools import (
                get_current_date_tool,
                get_current_time_tool,
                get_current_datetime_tool
            )
            from core.tools.local.math_tools import math_calculator_tool
            print("✅ Tools modules imported successfully")
        except ImportError as e:
            self.fail(f"Failed to import tools: {e}")

    def test_agent_state_creation(self):
        """Test that AgentState can be created."""
        try:
            from core.state import AgentState
            
            # Test creating an empty state
            state = AgentState()
            self.assertIsInstance(state, dict)
            
            # Test creating state with initial data
            initial_state = AgentState(
                user_input="test input",
                messages=[],
                final_answer="",
                error=None
            )
            self.assertEqual(initial_state["user_input"], "test input")
            self.assertEqual(initial_state["messages"], [])
            print("✅ AgentState creation successful")
        except Exception as e:
            self.fail(f"Failed to create AgentState: {e}")

    def test_prompt_manager_creation(self):
        """Test that PromptManager can be created and used."""
        try:
            from models.prompt_manager import PromptManager
            
            pm = PromptManager(max_context_tokens=2048, reserved_tokens=50)
            self.assertEqual(pm.max_context_tokens, 2048)
            self.assertEqual(pm.reserved_tokens, 50)
            
            # Test token counting
            test_text = "Hello, world!"
            token_count = pm.count_tokens(test_text)
            self.assertIsInstance(token_count, int)
            self.assertGreater(token_count, 0)
            print(f"✅ PromptManager creation successful, counted {token_count} tokens for '{test_text}'")
        except Exception as e:
            self.fail(f"Failed to create PromptManager: {e}")

    @patch('requests.post')
    def test_llm_provider_creation(self, mock_post):
        """Test that LlamaCppChatModel can be created."""
        try:
            from models.llamacpp_provider import LlamaCppChatModel
            import config
            
            llm = LlamaCppChatModel(
                api_url=config.LLAMACPP_API_URL,
                max_tokens=int(config.LLAMACPP_MAX_TOKENS)
            )
            self.assertEqual(llm.api_url, config.LLAMACPP_API_URL)
            self.assertEqual(llm.max_tokens, int(config.LLAMACPP_MAX_TOKENS))
            print("✅ LlamaCppChatModel creation successful")
        except Exception as e:
            self.fail(f"Failed to create LlamaCppChatModel: {e}")

    def test_graph_creation(self):
        """Test that the agent graph can be created."""
        try:
            from core.graphs.agent_graph import create_agent_graph
            
            graph = create_agent_graph()
            self.assertIsNotNone(graph)
            print("✅ Agent graph creation successful")
        except Exception as e:
            self.fail(f"Failed to create agent graph: {e}")

    def test_orchestrator_creation(self):
        """Test that AgentOrchestrator can be created."""
        try:
            from core.graphs.orchestrator import AgentOrchestrator
            
            orchestrator = AgentOrchestrator()
            self.assertIsNotNone(orchestrator)
            self.assertIsNotNone(orchestrator.graph)
            print("✅ AgentOrchestrator creation successful")
        except Exception as e:
            self.fail(f"Failed to create AgentOrchestrator: {e}")


def main():
    """Run the basic setup tests."""
    print("🧪 Running DaBot Agent Basic Setup Tests")
    print("=" * 50)
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestLangGraphSetup)
    
    # Run tests with verbose output
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n" + "=" * 50)
    if result.wasSuccessful():
        print("🎉 All basic setup tests passed!")
        print("✅ Your DaBot Agent setup is working correctly.")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        print(f"Failed: {len(result.failures)}, Errors: {len(result.errors)}")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
